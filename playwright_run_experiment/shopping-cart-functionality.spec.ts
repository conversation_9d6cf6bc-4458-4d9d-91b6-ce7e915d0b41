import { test, expect } from "@playwright/test"

const BASE_URL = "https://www.saucedemo.com/"

test("Add a product to the cart and verify cart icon updates", async ({ page }) => {
  // Go to Login Page
  await page.goto(BASE_URL)

  // Log in
  await page.fill('input[data-test="username"]', "standard_user")
  await page.fill('input[data-test="password"]', "secret_sauce")
  await page.click('input[data-test="login-button"]')

  // Wait for Inventory Page
  await expect(page).toHaveURL(/inventory/)

  // Check cart icon is initially empty
  const cartBadge = page.locator(".shopping_cart_badge")
  await expect(cartBadge).toHaveCount(0)

  // Click "Add to cart" for first product
  await page.click('button[data-test^="add-to-cart-"]')

  // Cart icon should update to "1"
  await expect(cartBadge).toHaveText("1")
})
