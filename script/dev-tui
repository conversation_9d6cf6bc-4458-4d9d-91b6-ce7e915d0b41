#!/bin/bash

# <PERSON><PERSON>t to run the TUI with the correct working directory
# Usage: ./script/dev-tui

set -e

# Get the script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "Building TUI..."
cd "$PROJECT_ROOT/packages/tui"
go build -o opencode ./cmd/opencode

echo "Starting TUI with project root: $PROJECT_ROOT"
./opencode --directory "$PROJECT_ROOT"
